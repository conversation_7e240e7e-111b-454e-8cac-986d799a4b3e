import axios from 'axios';
import { decryptAesCBC } from '@/lib/crypto';

export async function verifyDomain(domain: string) {
    try {
        const res = await axios.get(
            `https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=${domain}`
        );

        const encrypted = res.data.data;
        const decrypted = decryptAesCBC(encrypted);

        if (!decrypted) return null;

        return JSON.parse(decrypted);
    } catch (err) {
        console.error('Lỗi xác thực domain:', err);
        return null;
    }
}
