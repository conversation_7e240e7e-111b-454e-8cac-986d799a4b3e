'use client';

import React, { useState, useEffect } from 'react';
import { useDomainAuthContext } from '@/components/auth/DomainAuthProvider';
import { getAuthData } from '@/lib/auth';
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'

const Home = () => {
    const { websiteInfo, websiteAuth, isVerified } = useWebsiteInfo();
    const { isLoading, error } = useDomainAuthContext();
    const [authData, setAuthData] = useState<any>(null);

    useEffect(() => {
        // Only run on client side
        setAuthData(getAuthData());
    }, []);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin inline-block w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mb-4"></div>
                    <p><PERSON><PERSON> tải...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-lg text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">❌ Lỗi</h2>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
                    >
                        Thử lại
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            {websiteInfo?.logo && (
                                <img
                                    src={websiteInfo.logo}
                                    alt="Logo"
                                    className="w-12 h-12 rounded-lg"
                                />
                            )}
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">
                                    {websiteInfo?.meta_title || 'Website'}
                                </h1>
                                {websiteInfo?.meta_description && (
                                    <p className="text-gray-600 text-sm">
                                        {websiteInfo.meta_description}
                                    </p>
                                )}
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            {authData ? (
                                <a
                                    href="/logout"
                                    className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
                                >
                                    Đăng xuất
                                </a>
                            ) : (
                                <a
                                    href="/login"
                                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                                >
                                    Đăng nhập
                                </a>
                            )}
                            <a
                                href="/welcome"
                                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
                            >
                                Thông tin
                            </a>
                        </div>
                    </div>
                </div>

                {/* Domain Status */}
                <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 className="text-xl font-semibold mb-4">Trạng thái Domain</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-4 rounded">
                            <h3 className="font-medium text-gray-700">Xác thực</h3>
                            <p className={`text-lg font-semibold ${isVerified ? 'text-green-600' : 'text-red-600'}`}>
                                {isVerified ? '✅ Đã xác thực' : '❌ Chưa xác thực'}
                            </p>
                        </div>

                    </div>
                </div>

                {/* Auth Status */}
                {authData && (
                    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                        <h2 className="text-xl font-semibold mb-4">Trạng thái đăng nhập</h2>
                        <div className="bg-green-50 p-4 rounded border border-green-200">
                            <p className="text-green-800">
                                ✅ Đã đăng nhập {authData.user?.provider ? `qua ${authData.user.provider}` : ''}
                            </p>
                            {authData.user?.email && (
                                <p className="text-sm text-green-600 mt-1">
                                    Email: {authData.user.email}
                                </p>
                            )}
                        </div>
                    </div>
                )}

                {/* Google Login Config */}
                {websiteAuth && (
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-xl font-semibold mb-4">Cấu hình Google Login</h2>
                        <div className="space-y-3">
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Trạng thái:</span>
                                <span className={`px-3 py-1 rounded text-sm ${websiteAuth.google_login
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                    }`}>
                                    {websiteAuth.google_login ? 'Đã kích hoạt' : 'Chưa kích hoạt'}
                                </span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Client ID:</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {websiteAuth.client_id ? `${websiteAuth.client_id.substring(0, 20)}...` : 'Chưa có'}
                                </span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Client Secret:</span>
                                <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                    {websiteAuth.client_secret ? '***' : 'Chưa có'}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
export default Home;
