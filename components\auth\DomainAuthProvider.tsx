"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { decryptAesCBC } from "@/lib/crypto";
import { verifyDomain } from "@/services/verifyDomain";
import { fetchWebInfo, fetchWebsiteAuth } from "@/services/authService";

interface DomainAuthContextProps {
    isLoading: boolean;
    isVerified: boolean;
    error?: string;
}

const DomainAuthContext = createContext<DomainAuthContextProps>({
    isLoading: true,
    isVerified: false,
});

export const useDomainAuthContext = () => useContext(DomainAuthContext);

export const DomainAuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | undefined>(undefined);

    
    return (
        <DomainAuthContext.Provider value={{ isLoading, isVerified: true, error }}>
            {children}
        </DomainAuthContext.Provider>
    );
};



//https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.trinhxuanthuy.id.vn
