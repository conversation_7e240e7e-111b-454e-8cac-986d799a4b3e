"use client";

import React, { createContext, useContext, useEffect } from "react";
import { useRouter } from "next/navigation";
import { decryptAesCBC } from "@/lib/crypto";
import { verifyDomain } from "@/services/verifyDomain";
import { fetchWebInfo, fetchWebsiteAuth } from "@/services/authService";
import { useWebsiteStore } from "@/stores/websiteStore";
import { getDomainForApi } from "@/helpers/domainHelper";

interface DomainAuthContextProps {
    isLoading: boolean;
    isVerified: boolean;
    error?: string;
}

const DomainAuthContext = createContext<DomainAuthContextProps>({
    isLoading: true,
    isVerified: false,
});

export const useDomainAuthContext = () => useContext(DomainAuthContext);

export const DomainAuthProvider = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const {
        isVerified,
        isLoading,
        error,
        setLoading,
        setError,
        setDomainData,
        setWebsiteInfo,
        setWebsiteAuth
    } = useWebsiteStore();

    useEffect(() => {
        // Chỉ chạy 1 lần duy nhất khi chưa verify
        if (isVerified || isLoading) return;

        const initDomain = async () => {
            console.log('🚀 Starting domain verification...');
            setLoading(true);
            setError(null);

            try {
                // Bước 1: Verify domain và giải mã data
                const domain = getDomainForApi();
                console.log('🌐 Using domain for API:', domain);

                const res = await verifyDomain(domain);
                console.log('📦 API Response:', res);

                if (res.error === false && res.status === 200 && res.data) {
                    const decrypted = decryptAesCBC(res.data);
                    if (!decrypted) throw new Error('Giải mã thất bại');

                    const parsed = JSON.parse(decrypted);
                    console.log('📋 Parsed domain data:', parsed);

                    // Lưu domain data vào store
                    setDomainData(parsed);

                    // Bước 2: Fetch website info
                    try {
                        const webInfo = await fetchWebInfo(parsed.website.website_id);
                        setWebsiteInfo(webInfo);
                    } catch (webInfoError) {
                        console.error('Failed to fetch website info:', webInfoError);
                    }

                    // Bước 3: Fetch website auth
                    try {
                        const webAuth = await fetchWebsiteAuth(parsed.website.website_id);
                        setWebsiteAuth(webAuth);
                    } catch (webAuthError) {
                        console.error('Failed to fetch website auth:', webAuthError);
                    }

                    // Chuyển đến trang welcome nếu xác thực thành công
                    console.log('✅ Domain verification successful, redirecting to /welcome');
                    router.push('/welcome');

                } else {
                    throw new Error('Domain verification failed');
                }

            } catch (err: any) {
                console.error("❌ Domain initialization failed:", err);
                setError(err.message || "Domain verification failed");
            } finally {
                setLoading(false);
            }
        };

        initDomain();
    }, [isVerified, isLoading, setLoading, setError, setDomainData, setWebsiteInfo, setWebsiteAuth, router]);

    return (
        <DomainAuthContext.Provider value={{ isLoading, isVerified }}>
            {children}
        </DomainAuthContext.Provider>
    );
};



//https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.trinhxuanthuy.id.vn
