import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import axiosClient from "@/lib/axios";
import { decryptAesCBC } from "@/lib/crypto";
import axios from '@/lib/axios'



const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;
const client = axiosClient(baseURL);
// Domain verification
export const verifyDomain = async (payload: { domain: string }): Promise<any> => {
    try {
        const endpoint = getApiEndpoint([EP.API, EP.V1, EP.GLOBAL, EP.PROMPT, EP.DOMAIN], { [QUERY.DOMAIN]: payload.domain });

        if (process.env.NODE_ENV !== "production") {
            console.log("🔍 verifyDomain call:", `${baseURL}${endpoint}`);
        }

        const { data } = await client.get<any>(endpoint);

        // Ki<PERSON>m tra response format và giải mã data
        if (data.error === false && data.status === 200 && data.data && typeof data.data === 'string') {
            const decryptedData = decryptAesCBC(data.data);
            if (decryptedData) {
                try {
                    // Parse JSON nếu data đã giải mã là JSON
                    const parsedData = JSON.parse(decryptedData);
                    // Trả về response với data đã giải mã
                    return {
                        ...data,
                        data: parsedData
                    };
                } catch (parseError) {
                    // Nếu không phải JSON, sử dụng string đã giải mã
                    return {
                        ...data,
                        data: decryptedData
                    };
                }
            } else {
                console.error("Failed to decrypt domain data");
                throw new Error("Không thể giải mã dữ liệu domain");
            }
        }

        return data;
    } catch (error) {
        console.error('Error verifying domain:', error);
        throw error;
    }
};

// Google login
export const loginWithGoogle = async (payload: any): Promise<any> => {

    const endpoint = getApiEndpoint([EP.API, EP.V1, EP.USER, EP.LOGIN, EP.GOOGLE]);
    const { data } = await client.post<any>(endpoint, payload);

    console.log("1. loginWithGoogle response:", data);

    // Lưu nguyên token string không giải mã
    if (data.data && typeof data.data === 'string') {
        console.log("2. Keeping Google login token as string:", data.data);

        return {
            ...data,
            data: data.data
        };
    }

    return data;
};

// Email/Password login
export const loginWithEmail = async (payload: any): Promise<any> => {
    const endpoint = getApiEndpoint([EP.API, EP.V1, EP.USER, EP.LOGIN]);
    const { data } = await client.post<any>(endpoint, payload);

    console.log("1. loginWithEmail response:", data);

    // Lưu nguyên token string không giải mã
    if (data.data && typeof data.data === 'string') {
        console.log("2. Keeping email login token as string:", data.data);

        return {
            ...data,
            data: data.data
        };
    }

    return data;
};

export const fetchInitData = async (domain?: string) => {
    const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;
    const axiosInstance = axios(baseURL);

    // Import helper function
    const { getDomainForApi } = await import('@/helpers/domainHelper');

    // Xác định domain để sử dụng
    const domainToUse = domain || getDomainForApi();

    try {
        const response = await axiosInstance.get(`/api/v1/global/prompt/domain?domain=${domainToUse}`);
        const { data } = response;

        // Kiểm tra response format và giải mã data nếu cần
        if (data.error === false && data.status === 200 && data.data && typeof data.data === 'string') {
            const decryptedData = decryptAesCBC(data.data);
            if (decryptedData) {
                try {
                    const parsedData = JSON.parse(decryptedData);
                    return {
                        ...response,
                        data: {
                            ...data,
                            data: parsedData
                        }
                    };
                } catch (parseError) {
                    console.error("Failed to parse decrypted data:", parseError);
                    throw new Error("Dữ liệu giải mã không hợp lệ");
                }
            } else {
                console.error("Failed to decrypt init data");
                throw new Error("Không thể giải mã dữ liệu khởi tạo");
            }
        }

        return response;
    } catch (error) {
        console.error('fetchInitData error:', error);
        throw error;
    }
}

