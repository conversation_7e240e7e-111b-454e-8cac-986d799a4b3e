import { create } from 'zustand';

type WebsiteInfo = {
    meta_title?: string;
    meta_description?: string;
    favicon?: string;
    logo?: string;
    thumbnail?: string;
};

type WebsiteAuth = {
    google_login?: number;
    client_id?: string;
    client_secret?: string;
    website_id?: string;
    prompt_id?: string;
};

type WebsiteStore = {
    websiteInfo: WebsiteInfo;
    websiteAuth: WebsiteAuth;
    setWebsiteInfo: (info: WebsiteInfo) => void;
    setWebsiteAuth: (auth: WebsiteAuth) => void;
};

export const websiteStore = create<WebsiteStore>((set) => ({
    websiteInfo: {},
    websiteAuth: {},
    setWebsiteInfo: (info) => set({ websiteInfo: info }),
    setWebsiteAuth: (auth) => set({ websiteAuth: auth }),
}));
